import { useState } from 'react';

const usePageSize = (options = {}) => {
    const {
        cardWidth = 300,
        cardHeight = 220,
        headerHeight = 200,
        paginationHeight = 80,
        pageMargin = 40,
        minItems = 40,
        pageName = '页面'
    } = options;

    const [pageSize] = useState(() => {
        const screenWidth = window.innerWidth;
        const screenHeight = window.innerHeight;


        const availableWidth = screenWidth - pageMargin; // 减去页面左右边距
        const availableHeight = screenHeight - headerHeight - paginationHeight;

        const cols = Math.floor(availableWidth / cardWidth);
        const rows = Math.floor(availableHeight / cardHeight);

        const maxItems = Math.max(cols * rows, minItems); // 最少 minItems 条


        return maxItems;
    });

    return pageSize;
};

export default usePageSize;
